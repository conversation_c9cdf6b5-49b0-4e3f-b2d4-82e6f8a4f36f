%% State-Space Model Identification and Validation

close all;
clc;

%% 2. Main Program
%% Loading Data
% Define the database filename and .mat filename
db_filename = './webApi/data/measure_20250601.sqlite';
[~, name, ~] = fileparts(db_filename);  % Get the database filename (without extension)
mat_filename = ['./data/' name '.mat'];  % Path and filename for saving (.mat format)

% Check if the file already exists
if exist(mat_filename, 'file')
    % If the file exists, load the data
    disp('Loading existing measures_df data...');
    load(mat_filename, 'measures_df');
else
    % If the file doesn't exist, load data from the database
    disp('Loading data from database...');
    conn = sqlite(db_filename);
    
    % Build SQL query
    sql_query = ['SELECT speed, measureEntryTemp, measureExitTemp, setpointExitTemp, ' ...
                 'thick1, width1, weldPercent1, thick2, width2, weldPercent2, ' ...
                 'hd1, hd2, hd3, hd4, hd5, hd6, hd7, hd8, hd9, hd10, ' ...
                 'hd11, hd12, hd13, hd14, hd15, hd16, hd17, hd18, hd19, hd20, ' ...
                 'hd21, hd22, hd23, hd24, hd25, ' ...
                 'tt1, tt2, tt3, tt4, tt5, tt6, tt7, tt8, tt9, tt10, ' ...
                 'tt11, tt12, tt13, tt14, tt15, tt16, tt17, tt18, tt19, tt20, ' ...
                 'tt21, tt22, tt23, tt24, tt25 ' ...
                 'FROM measures'];
             
    % Execute query and get data
    measures_df = fetch(conn, sql_query);
    
    % Close database connection
    close(conn);
    
    % Calculate average power
    avg_power = ((measures_df.hd1 + measures_df.hd25) * 700 + ...
                (measures_df.hd9 + measures_df.hd23) * 13 * 165 + ...
                (measures_df.hd2 + measures_df.hd3 + measures_df.hd4 + measures_df.hd5 + ...
                 measures_df.hd6 + measures_df.hd7 + measures_df.hd8 + measures_df.hd10 + ...
                 measures_df.hd11 + measures_df.hd12 + measures_df.hd13 + measures_df.hd14 + ...
                 measures_df.hd15 + measures_df.hd16 + measures_df.hd17 + measures_df.hd18 + ...
                 measures_df.hd19 + measures_df.hd20 + measures_df.hd21 + measures_df.hd22 + ...
                 measures_df.hd24) * 14 * 165) / 54200;

    % Add average power to measures_df
    measures_df.avg_power = avg_power;

    % Convert thickness from milimeters to micrometers
    measures_df.thick1 = measures_df.thick1 * 1000;
    measures_df.thick2 = measures_df.thick2 * 1000;

    % Remove rows with NaN values
    rows_before = height(measures_df);
    measures_df = rmmissing(measures_df);
    rows_after = height(measures_df);
    disp(['Original row count: ', num2str(rows_before)]);
    disp(['Clean data row count: ', num2str(rows_after)]);
    disp(['Removed ', num2str(rows_before - rows_after), ' rows with NaN values']);

    % Remove rows where speed is 0
    zero_speed_rows = sum(measures_df.speed == 0);
    measures_df = measures_df(measures_df.speed > 0, :);
    disp(['Removed ', num2str(zero_speed_rows), ' rows where speed = 0']);
    disp(['Final row count: ', num2str(height(measures_df))]);

    % Save measures_df to .mat file
    save(mat_filename, 'measures_df');
end

%% 3. Define State, Input, and Output Columns
% Define tube temperature columns
tube_temp_cols = cell(1, 25);
for i = 1:25
    tube_temp_cols{i} = ['tt' num2str(i)];
end

state_cols = [tube_temp_cols, {'measureExitTemp'}];  % Include measureExitTemp as a state
input_cols = {'avg_power', 'speed', 'thick1', 'width1', 'weldPercent1', ...
              'thick2', 'width2', 'weldPercent2', 'measureEntryTemp'};
output_col = 'measureExitTemp';

%% 4. Split Data for Training and Testing
train_ratio = 0.9;
total_samples = height(measures_df);
train_num = floor(total_samples * train_ratio);
test_num = total_samples - train_num;

disp(['Total samples: ', num2str(total_samples)]);
disp(['Training samples: ', num2str(train_num)]);
disp(['Testing samples: ', num2str(test_num)]);
disp(['State variables: ', num2str(length(state_cols))]);
disp(['Input variables: ', num2str(length(input_cols))]);
disp(['State columns: ', strjoin(state_cols, ', ')]);
disp(['Input columns: ', strjoin(input_cols, ', ')]);
disp(['Output column: ', output_col]);

%% 5. Prepare Data
% Extract state and input data
X = table2array(measures_df(:, state_cols));
U = table2array(measures_df(:, input_cols));
Y = table2array(measures_df(:, output_col));

% Split into training and testing sets
X_train = X(1:train_num, :);
X_test = X(train_num+1:train_num+test_num, :);
U_train = U(1:train_num, :);
U_test = U(train_num+1:train_num+test_num, :);
Y_train = Y(1:train_num, :);
Y_test = Y(train_num+1:train_num+test_num, :);

%% 6. Identify A and B Matrices
[A, B] = identify_ab(U_train, X_train);

% Define known C matrix - zeros with 1 at the position of measureExitTemp
C = zeros(1, length(state_cols));
C(1, end) = 1;  % Set 1 for the last element (measureExitTemp)

disp(['A matrix shape: ', mat2str(size(A))]);
disp(['B matrix shape: ', mat2str(size(B))]);
disp(['C matrix shape: ', mat2str(size(C))]);

%% 7. Save the Identified Model
model_dir = save_model(A, B, C, state_cols, input_cols, output_col, train_ratio);
disp(['Model saved to: ', model_dir]);

%% 8. Multi-step Prediction with Periodic Calibration
Y_pred = predict_exit_temp(A, B, U_test, X_test);
Y_test_adjusted = Y_test(1:end-1);  % Remove the last sample as we're predicting one step ahead

%% 9. Calculate Error and Performance Metrics
error = Y_test_adjusted - Y_pred;
rmse = sqrt(mean(error.^2));
disp(['RMSE on test data: ', num2str(rmse)]);

%% 10. Plot Results
plot_results(Y_test_adjusted, Y_pred, error, X_test(1:end-1,:), U_test(1:end-1,:), state_cols, input_cols);


%% Subfunction Definitions

% Predict Using State-Space Model
function Y_pred = predict(A, B, C, U, X0)
    N_samples = size(U, 1);
    Y_pred = zeros(N_samples, size(C, 1));
    X = X0;
    
    for k = 1:N_samples
        Y_pred(k, :) = C * X;
        X = A * X + B * U(k, :)';
    end
end

% Identify A and B Matrices
function [A, B] = identify_ab(U, X)
    X_k = X(1:end-1, :);
    X_k1 = X(2:end, :);
    U_k = U(1:end-1, :);
    
    Phi = [X_k, U_k];
    
    A = [];
    B = [];
    
    for i = 1:size(X, 2)
        theta = Phi \ X_k1(:, i);
        A = [A; theta(1:size(X, 2))'];
        B = [B; theta(size(X, 2)+1:end)'];
    end
end


% Predict Exit Temperature with Periodic Calibration
function Y_pred = predict_exit_temp(A, B, U, X)
    N_samples = size(U, 1);
    
    % Extract the last row of A and B matrices (which predict measureExitTemp)
    A_exit = A(end, :);
    B_exit = B(end, :);
    

    calibration_interval = 50; 
    
    % 创建状态变量的副本，以便修改
    x_current = X(1, :)';
    
    Y_pred = zeros(N_samples-1, 1);
    
    for k = 1:N_samples-1
        % 使用系统动态方程: x(k+1) = A*x(k) + B*u(k)
        % 仅关注最后一行方程（出口温度）
        next_exit_temp = A_exit * x_current + B_exit * U(k, :)';
        Y_pred(k) = next_exit_temp;
        
        % 准备下一步的状态向量
        if k < N_samples-1
            % 复制下一时刻的管温度（除出口温度外的所有状态）
            x_current(1:end-1) = X(k+1, 1:end-1)';
            
            % 如果到达校准间隔，使用真实出口温度校准
            if mod(k, calibration_interval) == 0
                x_current(end) = X(k+1, end)';  % 使用真实出口温度校准
            else
                % 否则使用预测的出口温度
                x_current(end) = next_exit_temp;
            end
        end
    end
end

% Plot Results
function plot_results(Y_true, Y_pred, error, states, inputs, state_cols, input_cols)
    % Use scientific color schemes
    colors = {
        '#0072BD',  % Blue
        '#D95319',  % Orange
        '#EDB120',  % Yellow
        '#7E2F8E',  % Purple
        '#77AC30',  % Green
        '#4DBEEE',  % Light Blue
        '#A2142F'   % Red
    };
    
    % 计算时间轴（从样本索引转换为小时）
    time_h = (1:length(Y_true))' * 5/3600; % 每个样本间隔5秒，转换为小时
    
    figure('Position', [100, 100, 1000, 800]);
    set(gcf, 'Color', 'w');
    
    % 1. Prediction results with error on secondary y-axis
    subplot(3, 2, 1);
    yyaxis left;
    plot(time_h, Y_true, 'LineWidth', 2, 'Color', colors{1}, 'DisplayName', '实际出口温度');
    hold on;
    plot(time_h, Y_pred, '--', 'LineWidth', 2, 'Color', colors{2}, 'DisplayName', '预测温度');
    ylabel('温度 (°C)');
    ylim([700 840]);
    
    yyaxis right;
    plot(time_h, error * 0.5, 'LineWidth', 1.5, 'HandleVisibility', 'off');
    ylabel('误差 (°C)');
    ylim([min(error)*0.7, 2*max(error)]);
    xlim([min(time_h), max(time_h)]);
    yline(0, 'k--', 'LineWidth', 2, 'HandleVisibility', 'off');
    
    xlabel('时间 (h)');
    title('出口温度多步预测与实际值对比');
    legend('Location', 'northwest', 'NumColumns', 2);
    grid on;
    
    % 2. Tube Temperatures with colormap (replacing Weld Percentage)
    subplot(3, 2, 2);
    
    % Get tube temperature states
    tube_temp_indices = find(contains(state_cols, 'tt'));
    tube_temps = states(:, tube_temp_indices);
    
    % Create colormap for tube temperature lines
    cmap = jet(length(tube_temp_indices));
    colormap(subplot(3, 2, 2), 'jet');
    
    % Plot each tube temperature with color from colormap
    hold on;
    for i = 1:length(tube_temp_indices)
        plot(time_h, tube_temps(:, i), 'LineWidth', 1, 'Color', cmap(i,:));
    end
    hold off;
    
    % Add colorbar to show temperature position mapping
    cb = colorbar;
    cb.Label.String = 'Tube Position';
    cb.Position(3) = 0.015; % 减小colorbar的宽度
    cb.Position(1) = cb.Position(1) + 0.02; % 向右移动colorbar，增加与主图的间隔
    caxis([1 length(tube_temp_indices)]);
    
    xlabel('Time (h)');
    ylabel('Temperature (°C)');
    title('Tube Temperatures');
    grid on;
    xlim([min(time_h), max(time_h)]);
    box on; % 确保显示完整边框
    
    % 3. Average Power (moved from position 5)
    subplot(3, 2, 3);
    plot(time_h, inputs(:, 1), 'LineWidth', 2, 'Color', colors{5});
    xlabel('Time (h)');
    ylabel('Value (%)');
    title('Tube Power');
    grid on;
    xlim([min(time_h), max(time_h)]);
    
    % 4. Entry Temperature and Speed on dual y-axes
    subplot(3, 2, 4);
    
    % Find entry temperature index
    entry_temp_index = find(strcmp(input_cols, 'measureEntryTemp'));
    if isempty(entry_temp_index)
        entry_temp_index = find(contains(lower(input_cols), 'entry') | contains(lower(input_cols), 'entrytemp'));
    end
    if isempty(entry_temp_index) && ~isempty(input_cols)
        entry_temp_index = length(input_cols);
    end
    
    % Find speed index
    speed_index = find(strcmp(input_cols, 'speed'));
    
    if ~isempty(entry_temp_index) && ~isempty(speed_index)
        % Left Y-axis - Entry Temperature
        yyaxis left;
        plot(time_h, inputs(:, entry_temp_index), 'LineWidth', 2, 'Color', colors{1});
        ylabel('Entry Temperature (°C)');
        
        % Right Y-axis - Speed
        yyaxis right;
        plot(time_h, inputs(:, speed_index), 'LineWidth', 2, 'Color', colors{2});
        ylabel('Speed (m/min)');
        
        title('Entry Temperature and Speed');
        % legend(input_cols{entry_temp_index}, input_cols{speed_index}, 'Location', 'northwest');
        grid on;
        xlim([min(time_h), max(time_h)]);
        xlabel('Time (h)');
    end
    
    % 5. thick1 and width1
    subplot(3, 2, 5);
    thick1_idx = find(strcmp(input_cols, 'thick1'));
    width1_idx = find(strcmp(input_cols, 'width1'));
    
    if ~isempty(thick1_idx) && ~isempty(width1_idx)
        yyaxis left;
        plot(time_h, inputs(:, thick1_idx), 'LineWidth', 2, 'Color', colors{1});
        ylabel('Thickness 1 (μm)');
        
        yyaxis right;
        plot(time_h, inputs(:, width1_idx), 'LineWidth', 2, 'Color', colors{2});
        ylabel('Width 1 (mm)');
        
        title('Material 1 Properties');
        legend(input_cols{thick1_idx}, input_cols{width1_idx}, 'Location', 'northwest');
        grid on;
        xlim([min(time_h), max(time_h)]);
        xlabel('Time (h)');
    end
    
    % 6. thick2 and width2
    subplot(3, 2, 6);
    thick2_idx = find(strcmp(input_cols, 'thick2'));
    width2_idx = find(strcmp(input_cols, 'width2'));
    
    if ~isempty(thick2_idx) && ~isempty(width2_idx)
        yyaxis left;
        plot(time_h, inputs(:, thick2_idx), 'LineWidth', 2, 'Color', colors{1});
        ylabel('Thickness 2 (μm)');
        
        yyaxis right;
        plot(time_h, inputs(:, width2_idx), 'LineWidth', 2, 'Color', colors{2});
        ylabel('Width 2 (mm)');
        
        title('Material 2 Properties');
        legend(input_cols{thick2_idx}, input_cols{width2_idx}, 'Location', 'northwest');
        grid on;
        xlim([min(time_h), max(time_h)]);
        xlabel('Time (h)');
    end
    
    % Save figure
    if ~exist('./fig', 'dir')
        mkdir('./fig');
    end
    
    % Add RMSE to the title
    rmse_text = sprintf('(RMSE = %.2f °C)', sqrt(mean(error.^2)));
    sgtitle(['状态空间模型多步预测结果 ' rmse_text], 'FontSize', 14);
    
    % saveas(gcf, './fig/MATLAB_ss_AB_id_results.png');
    exportgraphics(gcf, './paper_plot/MATLAB_ss_AB_id_results.png', 'Resolution', 300);
end

% Save Model
function model_dir = save_model(A, B, C, state_cols, input_cols, output_col, train_ratio)
    % Create models directory if it doesn't exist
    if ~exist('models', 'dir')
        mkdir('models');
    end
    
    % Get timestamp for unique filename
    timestamp = char(datetime('now', 'Format', 'yyyyMMdd_HHmmss'));
    model_dir = fullfile('models', ['ss_ab_model_' num2str(train_ratio)]);
    
    % Create directory for this specific model
    if ~exist(model_dir, 'dir')
        mkdir(model_dir);
    end
    
    % Save model information
    model_info = struct();
    model_info.state_variables = state_cols;
    model_info.input_variables = input_cols;
    if ischar(output_col)
        model_info.output_variables = {output_col};
    else
        model_info.output_variables = output_col;
    end
    model_info.A_shape = size(A);
    model_info.B_shape = size(B);
    model_info.C_shape = size(C);
    model_info.created = timestamp;
    
    % Save model info as JSON
    model_info_json = jsonencode(model_info, 'PrettyPrint', true);
    fid = fopen(fullfile(model_dir, 'model_info.json'), 'w');
    fprintf(fid, '%s', model_info_json);
    fclose(fid);
    
    % Save matrices as CSV files
    writematrix(A, fullfile(model_dir, 'A_matrix.csv'), 'Delimiter', ',', 'FileType', 'text');
    writematrix(B, fullfile(model_dir, 'B_matrix.csv'), 'Delimiter', ',', 'FileType', 'text');
    writematrix(C, fullfile(model_dir, 'C_matrix.csv'), 'Delimiter', ',', 'FileType', 'text');
    
    fprintf('Model saved to %s\n', model_dir);
end

% Load Model
function [A, B, C, model_info] = load_model(model_dir)
    % Load model information
    fid = fopen(fullfile(model_dir, 'model_info.json'), 'r');
    raw = fread(fid, '*char')';
    fclose(fid);
    model_info = jsondecode(raw);
    
    % Load matrices
    A = readmatrix(fullfile(model_dir, 'A_matrix.csv'));
    B = readmatrix(fullfile(model_dir, 'B_matrix.csv'));
    C = readmatrix(fullfile(model_dir, 'C_matrix.csv'));
end 