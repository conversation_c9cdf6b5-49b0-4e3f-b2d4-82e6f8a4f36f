%% State-Space Model Identification with Online Parameter Adaptation
% 实现基于递归最小二乘(RLS)的在线参数估计，对比固定参数模型与自适应模型的性能
clear;
close all;
clc;

%% 1. 数据加载
% 定义数据库文件名和.mat文件名
db_filename = './webApi/data/measure_20250601.sqlite';
[~, name, ~] = fileparts(db_filename);
mat_filename = ['./data/' name '.mat'];

% 检查文件是否存在
if exist(mat_filename, 'file')
    % 如果文件存在，加载数据
    disp('加载已存在的measures_df数据...');
    load(mat_filename, 'measures_df');
else
    % 如果文件不存在，从数据库加载数据
    disp('从数据库加载数据...');
    conn = sqlite(db_filename);
    
    % 构建SQL查询
    sql_query = ['SELECT speed, measureEntryTemp, measureExitTemp, setpointExitTemp, ' ...
                 'thick1, width1, weldPercent1, thick2, width2, weldPercent2, ' ...
                 'hd1, hd2, hd3, hd4, hd5, hd6, hd7, hd8, hd9, hd10, ' ...
                 'hd11, hd12, hd13, hd14, hd15, hd16, hd17, hd18, hd19, hd20, ' ...
                 'hd21, hd22, hd23, hd24, hd25, ' ...
                 'tt1, tt2, tt3, tt4, tt5, tt6, tt7, tt8, tt9, tt10, ' ...
                 'tt11, tt12, tt13, tt14, tt15, tt16, tt17, tt18, tt19, tt20, ' ...
                 'tt21, tt22, tt23, tt24, tt25 ' ...
                 'FROM measures'];
             
    % 执行查询并获取数据
    measures_df = fetch(conn, sql_query);
    
    % 关闭数据库连接
    close(conn);
    
    % 计算平均功率
    avg_power = ((measures_df.hd1 + measures_df.hd25) * 700 + ...
                (measures_df.hd9 + measures_df.hd23) * 13 * 165 + ...
                (measures_df.hd2 + measures_df.hd3 + measures_df.hd4 + measures_df.hd5 + ...
                 measures_df.hd6 + measures_df.hd7 + measures_df.hd8 + measures_df.hd10 + ...
                 measures_df.hd11 + measures_df.hd12 + measures_df.hd13 + measures_df.hd14 + ...
                 measures_df.hd15 + measures_df.hd16 + measures_df.hd17 + measures_df.hd18 + ...
                 measures_df.hd19 + measures_df.hd20 + measures_df.hd21 + measures_df.hd22 + ...
                 measures_df.hd24) * 14 * 165) / 54200;

    % 添加平均功率到measures_df
    measures_df.avg_power = avg_power;

    % 将厚度从毫米转换为微米
    measures_df.thick1 = measures_df.thick1 * 1000;
    measures_df.thick2 = measures_df.thick2 * 1000;

    % 移除包含NaN值的行
    rows_before = height(measures_df);
    measures_df = rmmissing(measures_df);
    rows_after = height(measures_df);
    disp(['原始行数: ', num2str(rows_before)]);
    disp(['清洗后行数: ', num2str(rows_after)]);
    disp(['移除了 ', num2str(rows_before - rows_after), ' 行含NaN值的数据']);

    % 移除速度为0的行
    zero_speed_rows = sum(measures_df.speed == 0);
    measures_df = measures_df(measures_df.speed > 0, :);
    disp(['移除了 ', num2str(zero_speed_rows), ' 行速度为0的数据']);
    disp(['最终行数: ', num2str(height(measures_df))]);

    % 保存measures_df到.mat文件
    save(mat_filename, 'measures_df');
end

%% 2. 定义状态、输入和输出列
% 定义管温度列
tube_temp_cols = cell(1, 25);
for i = 1:25
    tube_temp_cols{i} = ['tt' num2str(i)];
end

state_cols = [tube_temp_cols, {'measureExitTemp'}];  % 包括measureExitTemp作为状态
input_cols = {'avg_power', 'speed', 'thick1', 'width1', 'weldPercent1', ...
              'thick2', 'width2', 'weldPercent2', 'measureEntryTemp'};
output_col = 'measureExitTemp';

%% 3. 数据分割：前10%用于离线辨识，10%~20%用于测试和在线递推
offline_train_ratio = 0.6;  % 前10%用于离线辨识
online_test_ratio = 0.2;    % 10%~20%用于测试和在线递推
over_ratio = 0.01;

total_samples = height(measures_df);
offline_train_num = floor(total_samples * offline_train_ratio);
online_test_num = floor(total_samples * online_test_ratio);
over_num = floor(total_samples * over_ratio);

disp(['总样本数: ', num2str(total_samples)]);
disp(['离线训练样本数: ', num2str(offline_train_num)]);
disp(['在线测试样本数: ', num2str(online_test_num)]);
disp(['状态变量数: ', num2str(length(state_cols))]);
disp(['输入变量数: ', num2str(length(input_cols))]);
disp(['状态列: ', strjoin(state_cols, ', ')]);
disp(['输入列: ', strjoin(input_cols, ', ')]);
disp(['输出列: ', output_col]);

%% 4. 准备数据
% 提取状态和输入数据
X = table2array(measures_df(:, state_cols));
U = table2array(measures_df(:, input_cols));
Y = table2array(measures_df(:, output_col));

% 分割为离线训练集和在线测试集
X_offline_train = X(1:offline_train_num, :);
U_offline_train = U(1:offline_train_num, :);

X_online_test = X(offline_train_num+over_num:offline_train_num+over_num+online_test_num-1, :);
U_online_test = U(offline_train_num+over_num:offline_train_num+over_num+online_test_num-1, :);
Y_online_test = Y(offline_train_num+over_num:offline_train_num+over_num+online_test_num-1, :);

%% 5. 离线辨识初始AB模型（可选正则化）
disp('开始离线辨识初始AB模型...');
[A0, B0] = identify_ab_regularized(U_offline_train, X_offline_train);

% 定义已知的C矩阵 - 全0，只有measureExitTemp位置为1
C = zeros(1, length(state_cols));
C(1, end) = 1;  % 设置最后一个元素（measureExitTemp）为1

disp(['A矩阵尺寸: ', mat2str(size(A0))]);
disp(['B矩阵尺寸: ', mat2str(size(B0))]);
disp(['C矩阵尺寸: ', mat2str(size(C))]);

%% 6. 固定AB模型预测
disp('使用固定AB模型进行预测...');
disp('注意: 固定AB模型每100个点使用真实温度校准一次，以便公平比较');

% 设置校准频率
calibration_interval = 100;  % 每100个点校准一次

% 创建状态变量的副本，以便修改
x_current_fixed = X_online_test(1, :)';

% 用于固定AB模型的预测
Y_pred_fixed = zeros(online_test_num-1, 1);

for k = 1:online_test_num-1
    % 预测下一个状态
    x_pred_fixed = A0 * x_current_fixed + B0 * U_online_test(k, :)';
    
    % 记录预测的出口温度
    Y_pred_fixed(k) = C * x_pred_fixed;
    
    % 准备下一步的状态向量
    if k < online_test_num-1
        % 复制下一时刻的管温度（除出口温度外的所有状态）
        x_current_fixed(1:end-1) = X_online_test(k+1, 1:end-1)';
        
        % 如果到达校准间隔，使用真实出口温度校准
        if mod(k, calibration_interval) == 0
            x_current_fixed(end) = X_online_test(k+1, end)';  % 使用真实出口温度校准
        else
            % 否则使用预测的出口温度
            x_current_fixed(end) = x_pred_fixed(end);
        end
    end
end

Y_online_test_adjusted = Y_online_test(1:end-1);  % 移除最后一个样本，因为我们预测的是前向一步


%% 7. 在线递推AB（递归最小二乘RLS）
disp('开始在线参数递推（递归最小二乘RLS）...');
disp('注意: 在线模型每100个点更新参数，同时使用真实温度校准');

% 参数初始化
n_x = size(X_offline_train, 2);
n_u = size(U_offline_train, 2);

% 初始化A和B矩阵
A_k = A0;
B_k = B0;

% 在线预测结果存储
Y_pred_online = zeros(online_test_num-1, 1);

% 设置RLS更新频率
rls_update_interval = 100;  % 每100个点更新一次参数
disp(['RLS更新频率: 每', num2str(rls_update_interval), '个数据点']);

% 进度显示
fprintf('开始RLS递推更新: ');
progress_step = floor(online_test_num/10);

% 初始化历史记录索引
history_idx = 1;

% 存储参数历史（每100步存储一次）
theta_history = zeros(n_x * n_x + n_x * n_u, ceil((online_test_num-1)/rls_update_interval));

% 初始化RLS参数
% 为每个状态方程分别设置RLS
P_rls = cell(n_x, 1);  % 协方差矩阵
theta_rls = cell(n_x, 1);  % 参数向量

% 初始化RLS参数
for i = 1:n_x
    n_params = n_x + n_u;  % 每个状态方程的参数数量
    P_rls{i} = eye(n_params) * 1000;  % 初始协方差矩阵（较大值表示低置信度）
    theta_rls{i} = [A_k(i,:)'; B_k(i,:)'];  % 初始参数估计
end

% 遗忘因子（可调整，接近1表示长期记忆，小于1表示更快适应变化）
forget_factor = 0.999;

% RLS递推
% 创建状态变量的副本，以便修改
x_current = X_online_test(1, :)';

for k = 1:online_test_num-1
    % 显示进度
    if mod(k, progress_step) == 0
        fprintf('%d%% ', round(k/online_test_num*100));
    end
    
    % 当前状态和输入
    u_k = U_online_test(k, :)';
    
    % 预测下一个状态
    x_pred = A_k * x_current + B_k * u_k;
    
    % 只在指定间隔更新参数
    if mod(k, rls_update_interval) == 0
        % 构建回归向量
        phi_k = [x_current; u_k];  % [x_k; u_k]
        
        % 真实的下一个状态（仅用于参数更新）
        x_next = X_online_test(k+1, :)';
        
        % 对每个状态方程分别更新参数
        for i = 1:n_x
            % 当前参数
            theta_i = theta_rls{i};
            P_i = P_rls{i};
            
            % 预测
            y_pred_i = phi_k' * theta_i;
            
            % 预测误差
            e_i = x_next(i) - y_pred_i;
            
            % 计算增益
            K_i = (P_i * phi_k) / (forget_factor + phi_k' * P_i * phi_k);
            
            % 更新参数
            theta_i = theta_i + K_i * e_i;
            
            % 更新协方差
            P_i = (P_i - K_i * phi_k' * P_i) / forget_factor;
            
            % 存回
            theta_rls{i} = theta_i;
            P_rls{i} = P_i;
            
            % 更新A和B矩阵
            A_k(i, :) = theta_i(1:n_x)';
            B_k(i, :) = theta_i(n_x+1:end)';
        end
        
        % 存储当前参数
        theta = [reshape(A_k, [], 1); reshape(B_k, [], 1)];
        theta_history(:, history_idx) = theta;
        history_idx = history_idx + 1;
        
        % 在更新参数后，也使用真实温度校准当前状态（确保与固定模型相同的校准频率）
        if k < online_test_num-1
            x_current(end) = X_online_test(k+1, end)';  % 使用真实出口温度校准
        end
    end
    
    % 用当前参数预测出口温度（只需要最后一行的状态方程）
    Y_pred_online(k) = C * x_pred;
    
    % 准备下一步的状态向量
    if k < online_test_num-1
        % 复制下一时刻的管温度（除出口温度外的所有状态）
        x_current(1:end-1) = X_online_test(k+1, 1:end-1)';
        % 使用预测的出口温度作为状态的最后一个元素
        x_current(end) = x_pred(end);
    end
end

fprintf('\nRLS递推完成!\n');

%% 8. 计算误差和性能指标

error_fixed = Y_online_test_adjusted - Y_pred_fixed;
error_online = Y_online_test_adjusted - Y_pred_online;

% 计算各种性能指标
rmse_fixed = sqrt(mean(error_fixed.^2));
rmse_online = sqrt(mean(error_online.^2));

mae_fixed = mean(abs(error_fixed));
mae_online = mean(abs(error_online));

% 计算R²值
SS_tot = sum((Y_online_test_adjusted - mean(Y_online_test_adjusted)).^2);
SS_res_fixed = sum(error_fixed.^2);
SS_res_online = sum(error_online.^2);
r2_fixed = 1 - SS_res_fixed/SS_tot;
r2_online = 1 - SS_res_online/SS_tot;

disp('性能指标对比:');
disp('-------------------------------------');
disp(['固定AB模型RMSE: ', num2str(rmse_fixed, '%.4f'), ' °C']);
disp(['在线递推AB模型RMSE: ', num2str(rmse_online, '%.4f'), ' °C']);
disp(['固定AB模型MAE: ', num2str(mae_fixed, '%.4f'), ' °C']);
disp(['在线递推AB模型MAE: ', num2str(mae_online, '%.4f'), ' °C']);
disp(['固定AB模型R²: ', num2str(r2_fixed, '%.4f')]);
disp(['在线递推AB模型R²: ', num2str(r2_online, '%.4f')]);
disp('-------------------------------------');

% 计算性能提升百分比
rmse_improve = (rmse_fixed - rmse_online) / rmse_fixed * 100;
mae_improve = (mae_fixed - mae_online) / mae_fixed * 100;
r2_improve = (r2_online - r2_fixed) / abs(r2_fixed) * 100;

disp(['RMSE改善: ', num2str(rmse_improve, '%.2f'), '%']);
disp(['MAE改善: ', num2str(mae_improve, '%.2f'), '%']);
disp(['R²改善: ', num2str(r2_improve, '%.2f'), '%']);

%% 9. 绘制结果

% 绘制结果对比图
plot_comparison_results(Y_online_test_adjusted, Y_pred_fixed, Y_pred_online, ...
                       error_fixed, error_online, rmse_fixed, rmse_online);

% 可视化参数变化（选择性展示部分参数）
% plot_parameter_evolution(theta_history, n_x, n_u);

%% 子函数定义

% 带正则化的AB辨识函数
function [A, B] = identify_ab_regularized(U, X)
        X_k = X(1:end-1, :);
        X_k1 = X(2:end, :);
        U_k = U(1:end-1, :);
        
        % 使用标准最小二乘法，避免矩阵病态问题
        disp('使用带正则化的最小二乘法辨识模型...');
        
        % 初始化A和B矩阵
        n_x = size(X, 2);
        n_u = size(U, 2);
        A = zeros(n_x, n_x);
        B = zeros(n_x, n_u);
        
        % 固定正则化参数（可以根据需要调整）
        lambda = 1e-3;
        disp(['使用固定正则化参数 lambda = ', num2str(lambda)]);
        
        % 对每个状态方程分别进行正则化最小二乘
        for i = 1:n_x
            % 构建回归矩阵和目标向量
            PHI = [X_k, U_k];
            Y = X_k1(:, i);
            
            % 正则化最小二乘求解 (PHI'*PHI + lambda*I)^(-1) * PHI' * Y
            n_params = size(PHI, 2);
            theta = (PHI' * PHI + lambda * eye(n_params)) \ (PHI' * Y);
            
            % 提取A和B矩阵的行
            A(i, :) = theta(1:n_x)';
            B(i, :) = theta(n_x+1:end)';
        end
        disp('模型辨识完成！');
end


% 预测出口温度 - 多步向前预测
function Y_pred = predict_exit_temp(A, B, U, X)
    N_samples = size(U, 1);
    
    % 提取A和B矩阵的最后一行（预测measureExitTemp）
    A_exit = A(end, :);
    B_exit = B(end, :);
    
    Y_pred = zeros(N_samples-1, 1);
    
    % 创建状态变量的副本，以便修改
    x_current = X(1, :)';
    
    for k = 1:N_samples-1
        % 使用系统动态方程: x(k+1) = A*x(k) + B*u(k)
        % 仅关注最后一行方程（出口温度）
        next_exit_temp = A_exit * x_current + B_exit * U(k, :)';
        Y_pred(k) = next_exit_temp;
        
        % 准备下一步的状态向量
        if k < N_samples-1
            % 复制下一时刻的管温度（除出口温度外的所有状态）
            x_current(1:end-1) = X(k+1, 1:end-1)';
            % 使用预测的出口温度作为状态的最后一个元素
            x_current(end) = next_exit_temp;
        end
    end
end

% 绘制比较结果
function plot_comparison_results(Y_true, Y_fixed, Y_online, error_fixed, error_online, rmse_fixed, rmse_online)
    % 使用科学配色方案
    colors = {
        '#0072BD',  % 蓝色
        '#D95319',  % 橙色
        '#EDB120',  % 黄色
        '#7E2F8E',  % 紫色
        '#77AC30',  % 绿色
        '#4DBEEE',  % 浅蓝色
        '#A2142F'   % 红色
    };
    
    % 确保所有向量长度一致
    min_len = min([length(Y_true), length(Y_fixed), length(Y_online)]);
    Y_true = Y_true(1:min_len);
    Y_fixed = Y_fixed(1:min_len);
    Y_online = Y_online(1:min_len);
    error_fixed = error_fixed(1:min_len);
    error_online = error_online(1:min_len);
    
    % 计算时间轴（从样本索引转换为小时）
    time_h = (1:min_len)' * 5/3600; % 每个样本间隔5秒，转换为小时
    
    figure('Position', [100, 100, 1000, 400]);
    set(gcf, 'Color', 'w');
    
    % 1. 预测结果对比
    subplot(1, 2, 1);
    plot(time_h, Y_true, 'LineWidth', 2, 'Color', 'k', 'DisplayName', '实际出口温度');
    hold on;
    plot(time_h, Y_fixed, '--', 'LineWidth', 1.5, 'Color', colors{1}, 'DisplayName', '固定AB预测(每100点校准)');
    plot(time_h, Y_online, '-.', 'LineWidth', 1.5, 'Color', colors{2}, 'DisplayName', '在线AB预测(每100点更新)');
    xlabel('时间 (h)');
    ylabel('温度 (°C)');
    title('出口温度预测对比 (多步预测+定期校准)');
    legend('Location', 'best');
    grid on;
    
    % 2. 误差对比
    subplot(1, 2, 2);
    plot(time_h, error_fixed, 'LineWidth', 1.5, 'Color', colors{1}, 'DisplayName', sprintf('固定AB (RMSE=%.2f)', rmse_fixed));
    hold on;
    plot(time_h, error_online, 'LineWidth', 1.5, 'Color', colors{2}, 'DisplayName', sprintf('在线AB (RMSE=%.2f)', rmse_online));
    yline(0, 'k--', 'LineWidth', 1);
    xlabel('时间 (h)');
    ylabel('预测误差 (°C)');
    title('预测误差对比');
    legend('Location', 'best');
    grid on;
    
    
    % 添加性能提升信息
    improvement = (rmse_fixed - rmse_online) / rmse_fixed * 100;
    if abs(improvement) > 1000
        improvement_str = '性能差异过大';
    else
        improvement_str = sprintf('性能提升: %.2f%%', improvement);
    end
    sgtitle(sprintf('固定AB(定期校准) vs 在线递推AB(参数更新)模型性能对比 (%s)', improvement_str), 'FontSize', 14);
    
    % 保存图形
    if ~exist('./fig', 'dir')
        mkdir('./fig');
    end
    exportgraphics(gcf, './fig/online_vs_fixed_model_comparison.png', 'Resolution', 300);
end

% 绘制参数演化
function plot_parameter_evolution(theta_history, n_x, n_u)
    figure('Position', [150, 150, 1200, 800]);
    set(gcf, 'Color', 'w');
    
    % 创建时间步数组（每100步更新一次）
    time_steps = (0:size(theta_history, 2)-1) * 100;
    
    % 选择部分重要参数可视化
    % 1. A矩阵的对角线元素（自回归系数）
    subplot(2, 2, 1);
    for i = 1:min(5, n_x)  % 最多显示5个状态的自回归系数
        plot(time_steps, theta_history((i-1)*n_x + i, :), 'LineWidth', 1.5, 'DisplayName', sprintf('A(%d,%d)', i, i));
        hold on;
    end
    xlabel('时间步 (每100步采样一次)');
    ylabel('参数值');
    title('A矩阵对角线元素（自回归系数）');
    legend('Location', 'best');
    grid on;
    
    % 2. 最后一行A矩阵（影响出口温度的状态系数）
    subplot(2, 2, 2);
    A_last_row_idx = (n_x-1)*n_x + 1 : n_x*n_x;
    for i = 1:min(5, n_x)  % 最多显示5个系数
        plot(time_steps, theta_history(A_last_row_idx(i), :), 'LineWidth', 1.5, 'DisplayName', sprintf('A(%d,%d)', n_x, i));
        hold on;
    end
    xlabel('时间步 (每100步采样一次)');
    ylabel('参数值');
    title('影响出口温度的状态系数');
    legend('Location', 'best');
    grid on;
    
    % 3. 最后一行B矩阵（影响出口温度的输入系数）
    subplot(2, 2, 3);
    B_last_row_idx = n_x*n_x + (n_x-1)*n_u + 1 : n_x*n_x + n_x*n_u;
    for i = 1:min(n_u, 5)  % 最多显示5个系数
        plot(time_steps, theta_history(B_last_row_idx(i), :), 'LineWidth', 1.5, 'DisplayName', sprintf('B(%d,%d)', n_x, i));
        hold on;
    end
    xlabel('时间步 (每100步采样一次)');
    ylabel('参数值');
    title('影响出口温度的输入系数');
    legend('Location', 'best');
    grid on;
    
    % 4. 参数变化率
    subplot(2, 2, 4);
    if size(theta_history, 2) > 1
        param_change = diff(theta_history, 1, 2);
        param_change_norm = sqrt(sum(param_change.^2, 1));
        plot(time_steps(1:end-1), param_change_norm, 'LineWidth', 1.5);
        xlabel('时间步 (每100步采样一次)');
        ylabel('参数变化率');
        title('参数变化率（欧几里得范数）');
        grid on;
    else
        text(0.5, 0.5, '参数历史记录不足，无法计算变化率', 'HorizontalAlignment', 'center');
        axis off;
    end
    
    sgtitle('在线参数估计演化 (每100步更新一次)', 'FontSize', 14);
    
    % 保存图形
    if ~exist('./fig', 'dir')
        mkdir('./fig');
    end
    exportgraphics(gcf, './fig/online_parameter_evolution.png', 'Resolution', 300);
end 